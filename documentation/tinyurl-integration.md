# Integración de TinyURL

## Descripción
Esta integración permite acortar automáticamente los enlaces de invitación generados por el sistema usando la API de TinyURL.

## Configuración

### 1. Variables de Entorno
Agrega las siguientes variables a tu archivo `.env`:

```env
# TinyURL Integration
TINYURL_API_BASE=https://api.tinyurl.com
TINYURL_API_TOKEN=tu_tinyurl_token_aqui
TINYURL_TIMEOUT_MS=10000
```

### 2. Obtener Token de TinyURL
1. Ve a [TinyURL API](https://tinyurl.com/app/dev)
2. Crea una cuenta o inicia sesión
3. Genera un API token
4. Copia el token y agrégalo a tu archivo `.env`

## Funcionamiento

### Acortamiento Automático
- Cuando se crea un registro de campaña, el sistema genera un enlace de invitación
- El enlace se envía automáticamente a TinyURL para ser acortado
- Si TinyURL falla, se usa el enlace original como fallback

### Estructura del Enlace Acortado
- **Alias**: `invite-{inviteCode}`
- **Descripción**: `Invite link for code: {inviteCode}`
- **Tags**: `invite,campaign`

## Archivos Modificados

### Nuevos Archivos
- `src/services/tinyurl.service.ts` - Servicio principal de TinyURL
- `src/services/tinyurl.module.ts` - Módulo de TinyURL
- `src/config/tinyurl.config.ts` - Configuración (ya existía)

### Archivos Modificados
- `src/app.module.ts` - Agregado TinyUrlModule
- `src/modules/campaign-registrations/campaign-registrations.service.ts` - Integrado TinyURL
- `src/modules/campaign-registrations/campaign-registrations.module.ts` - Importado TinyUrlModule
- `.env.example` - Agregadas variables de TinyURL

## Uso del Servicio

### Método Principal
```typescript
async createShortUrl(request: TinyUrlCreateRequest): Promise<string>
```

### Método Específico para Invitaciones
```typescript
async shortenInviteLink(originalUrl: string, inviteCode: string): Promise<string>
```

## Manejo de Errores
- Si TinyURL no está configurado correctamente, se registra un warning
- Si la API de TinyURL falla, se devuelve la URL original
- Todos los errores se registran en los logs para debugging

## Testing
Para probar la integración:

1. Configura las variables de entorno
2. Ejecuta el webhook de nuevo contacto
3. Verifica que el enlace de invitación sea una URL corta de TinyURL
4. Verifica que el enlace redirija correctamente al enlace original

## Logs
El servicio registra:
- Creación exitosa de URLs cortas
- Errores de la API de TinyURL
- Fallbacks a URLs originales
- Warnings de configuración
