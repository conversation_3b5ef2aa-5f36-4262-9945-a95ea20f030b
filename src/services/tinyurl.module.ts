import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { TinyUrlService } from './tinyurl.service';
import tinyurlConfig from '../config/tinyurl.config';

@Module({
  imports: [
    ConfigModule.forFeature(tinyurlConfig),
    HttpModule,
  ],
  providers: [TinyUrlService],
  exports: [TinyUrlService],
})
export class TinyUrlModule {}
