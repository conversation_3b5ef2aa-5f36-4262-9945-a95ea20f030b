import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

export interface TinyUrlCreateRequest {
  url: string;
  domain?: string;
  description?: string;
}

export interface TinyUrlCreateResponse {
  data: {
    domain: string;
    alias: string;
    deleted: boolean;
    archived: boolean;
    analytics: {
      enabled: boolean;
      public: boolean;
    };
    tags: string[];
    created_at: string;
    expires_at: string | null;
    tiny_url: string;
    url: string;
    description: string;
  };
  code: number;
  errors: string[];
}

@Injectable()
export class TinyUrlService {
  private readonly logger = new Logger(TinyUrlService.name);
  private readonly apiBase: string;
  private readonly apiToken: string | undefined;
  private readonly timeoutMs: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.apiBase = this.configService.get<string>('tinyurl.apiBase')!;
    this.apiToken = this.configService.get<string>('tinyurl.apiToken');
    this.timeoutMs = this.configService.get<number>('tinyurl.timeoutMs')!;

    if (!this.apiToken) {
      this.logger.warn('TinyURL API token not configured. Service will not work properly.');
    }
  }

  async createShortUrl(request: TinyUrlCreateRequest): Promise<string> {
    if (!this.apiToken) {
      this.logger.error('TinyURL API token not configured');
      throw new Error('TinyURL service not properly configured');
    }

    try {
      const url = `${this.apiBase}/create`;
      
      const headers = {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json',
      };

      const response = await firstValueFrom(
        this.httpService.post<TinyUrlCreateResponse>(url, request, {
          headers,
          timeout: this.timeoutMs,
        })
      );

      if (response.data.code === 0 && response.data.data?.tiny_url) {
        this.logger.log(`Successfully created short URL: ${response.data.data.tiny_url}`);
        return response.data.data.tiny_url;
      } else {
        this.logger.error('TinyURL API returned error:', response.data.errors);
        throw new Error(`TinyURL API error: ${response.data.errors?.join(', ') || 'Unknown error'}`);
      }
    } catch (error) {
      this.logger.error('Failed to create short URL:', error.message);
      
      // En caso de error, devolver la URL original como fallback
      this.logger.warn(`Returning original URL as fallback: ${request.url}`);
      return request.url;
    }
  }

  async shortenInviteLink(originalUrl: string, inviteCode: string): Promise<string> {
    const request: TinyUrlCreateRequest = {
      url: originalUrl,
      description: `Invite link for code: ${inviteCode}`,
    };

    return this.createShortUrl(request);
  }
}
