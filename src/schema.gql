# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type PoliticalParty {
  id: String!
  name: String!
  short_name: String!
  candidates: [Candidate!]
}

type Candidate {
  id: String!
  full_name: String!
  political_party_id: String
  political_party: PoliticalParty
  campaigns: [Campaign!]
}

type Campaign {
  id: String!
  name: String!
  candidate: Candidate
  whatsapp_number: String
  wati_tenant_id: String
  is_active: Boolean!
  start_date: Timestamp
  end_date: Timestamp
  campaign_registrations: [CampaignRegistration!]
}

"""
`Date` type as integer. Type represents date and time as number of milliseconds from start of UNIX epoch.
"""
scalar Timestamp

type CampaignRegistration {
  id: String!
  campaign_id: String
  campaign: Campaign
  user_id: String
  user: User
  name: String!
  invite_code: String!
  invite_link: String
  referred_by_user_id: String
  registration_form: String
  ai_analysis_enabled: Boolean
  integration_configuration: String
}

type Permission {
  id: String!
  name: String!
  description: String
  resource: String
  action: String
  isActive: Boolean!
  isParent: Boolean!
  hierarchyLevel: Float!
  roles: [Role!]
}

type Role {
  id: String!
  name: String!
  description: String
  isActive: Boolean!
  users: [String!]
  permissions: [Permission!]
}

type User {
  id: String!
  email: String
  firstName: String
  lastName: String
  phone: String!
  status: UserStatus!
  emailVerified: Boolean!
  roles: [Role!]
  campaign_registrations: [CampaignRegistration!]
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
}

type PermissionHierarchy {
  id: String!
  parentPermissionId: String!
  childPermissionId: String!
  isActive: Boolean!
  description: String
  parentPermission: Permission!
  childPermission: Permission!
}

type AuthResponseWithoutRefreshToken {
  user: User!
  accessToken: String!
  expiresIn: Float!
  refreshExpiresIn: Float!
}

type PermissionHierarchyEntry {
  parentPermission: String!
  childPermissions: [String!]!
}

type CacheStats {
  entries: Int!
  keys: [String!]!
}

type SystemStats {
  roles: Int!
  permissions: Int!
  users: Int!
  cache: CacheStats!
}

type UserRoleInfo {
  userId: ID!
  email: String!
  firstName: String
  lastName: String
  roleNames: [String!]!
}

type PermissionHierarchyMap {
  parentPermission: String!
  childPermissions: [String!]!
}

type PermissionHierarchyResponse {
  success: Boolean!
  message: String
  data: PermissionHierarchy
}

type PermissionHierarchyListResponse {
  success: Boolean!
  data: [PermissionHierarchy!]!
}

type PermissionHierarchyMapResponse {
  success: Boolean!
  data: [PermissionHierarchyMap!]!
}

type InheritedPermissionsResponse {
  success: Boolean!
  data: [String!]!
}

type SessionInfoDto {
  id: String!
  deviceInfo: String!
  ipAddress: String
  lastUsedAt: Timestamp!
  createdAt: Timestamp!
  isActive: Boolean!
  isCurrent: Boolean
}

type UserSessionsResponseDto {
  sessions: [SessionInfoDto!]!
  totalSessions: Float!
}

type SessionActionResponseDto {
  message: String!
  success: Boolean!
}

type Query {
  me: User!

  """Simple test query to verify GraphQL is working"""
  testAdminGraphQL: String!

  """Get all roles with their permissions"""
  getAllRoles: [Role!]!

  """Get a specific role by ID"""
  getRoleById(id: ID!): Role!

  """Get all roles assigned to a specific user"""
  getRolesByUser(userId: ID!): [Role!]!

  """Get all permissions in the system"""
  getAllPermissions: [Permission!]!

  """Get permissions filtered by resource"""
  getPermissionsByResource(resource: String!): [Permission!]!

  """Get all permissions for a specific user (direct permissions)"""
  getUserPermissions(userId: ID!): [String!]!

  """Get all effective permissions for a user (including inheritance)"""
  getUserEffectivePermissions(userId: ID!): [String!]!

  """Get the permission inheritance hierarchy configuration"""
  getPermissionHierarchy: [PermissionHierarchyEntry!]!

  """Check if a user has a specific permission"""
  userHasPermission(userId: ID!, permission: String!): Boolean!

  """Check if a user has all specified permissions"""
  userHasAllPermissions(userId: ID!, permissions: [String!]!): Boolean!

  """Check if a user has any of the specified permissions"""
  userHasAnyPermission(userId: ID!, permissions: [String!]!): Boolean!

  """Check if a user can access a resource with specific action"""
  userCanAccess(userId: ID!, resource: String!, action: String!): Boolean!

  """Get comprehensive system statistics"""
  getSystemStats: SystemStats!

  """Get all users assigned to a specific role"""
  getUsersByRole(roleId: ID!): [User!]!

  """Get all users with their assigned roles"""
  getAllUsersWithRoles: [UserRoleInfo!]!

  """Get detailed cache statistics"""
  getCacheStats: CacheStats!
  getInheritedPermissions(permissionName: String!): InheritedPermissionsResponse!

  """Check if a permission can inherit other permissions"""
  isInheritablePermission(permissionName: String!): Boolean!
  getAllPermissionHierarchies: PermissionHierarchyListResponse!
  getPermissionHierarchyMap: PermissionHierarchyMapResponse!
  getUserSessions: UserSessionsResponseDto!

  """Get all political parties"""
  getPoliticalParties: [PoliticalParty!]

  """Get political party by ID"""
  getPoliticalPartyById(
    """Political party ID"""
    id: String!
  ): PoliticalParty
  getCandidates: [Candidate!]!
  candidate(id: String!): Candidate!
}

type Mutation {
  createCampaign(createCampaignInput: CreateCampaignInput!): Campaign!
  updateCampaign(updateCampaignInput: UpdateCampaignInput!): Campaign!
  register(registerInput: RegisterDto!, appType: String): AuthResponseWithoutRefreshToken!
  login(loginInput: LoginDto!, appType: String): AuthResponseWithoutRefreshToken!
  refreshToken(appType: String): AuthResponseWithoutRefreshToken!

  """Create a new role"""
  createRole(input: CreateRoleDto!): Role!

  """Update an existing role"""
  updateRole(id: ID!, input: UpdateRoleDto!): Role!

  """Delete a role (soft delete)"""
  deleteRole(id: ID!): Boolean!

  """Assign a permission to a role"""
  assignPermissionToRole(roleId: ID!, permissionId: ID!): Role!

  """Remove a permission from a role"""
  removePermissionFromRole(roleId: ID!, permissionId: ID!): Role!

  """Assign a role to a user"""
  assignRoleToUser(userId: ID!, roleId: ID!): Boolean!

  """Remove a role from a user"""
  removeRoleFromUser(userId: ID!, roleId: ID!): Boolean!

  """Create a new permission"""
  createPermission(input: CreatePermissionDto!): Permission!

  """Clear cache for a specific user"""
  clearUserCache(userId: ID!): Boolean!

  """Clear all permission caches (use with caution)"""
  clearAllCache: Boolean!

  """Assign a role to multiple users at once"""
  bulkAssignRoleToUsers(userIds: [ID!]!, roleId: ID!): Boolean!

  """Remove a role from multiple users at once"""
  bulkRemoveRoleFromUsers(userIds: [ID!]!, roleId: ID!): Boolean!
  createPermissionHierarchy(input: CreatePermissionHierarchyDto!): PermissionHierarchyResponse!
  deletePermissionHierarchy(hierarchyId: String!): PermissionHierarchyResponse!
  revokeSession(sessionId: String!): SessionActionResponseDto!
  revokeAllSessions: SessionActionResponseDto!
  revokeOtherSessions: SessionActionResponseDto!

  """Create a new political party"""
  createPoliticalParty(
    """Political party data"""
    politicalParty: InsertPoliticalPartyInput!
  ): PoliticalParty!

  """Update an existing political party"""
  updatePoliticalParty(
    """Political party ID"""
    id: String!

    """Updated political party data"""
    politicalParty: UpdatePoliticalPartyInput!
  ): PoliticalParty!

  """Delete a political party"""
  deletePoliticalParty(
    """Political party ID"""
    id: String!
  ): Boolean!
  createCandidate(createCandidateInput: CreateCandidateInput!): Candidate!
  updateCandidate(updateCandidateInput: UpdateCandidateInput!): Candidate!
  deleteCandidate(id: String!): Candidate!
}

input CreateCampaignInput {
  name: String!
  candidate_id: String!
  whatsapp_number: String!
  wati_tenant_id: String!
  is_active: Boolean!
  start_date: Timestamp
  end_date: Timestamp
  token: String!
}

input UpdateCampaignInput {
  id: String!
  name: String!
  candidate_id: String!
  whatsapp_number: String!
  wati_tenant_id: String!
  is_active: Boolean!
  start_date: Timestamp
  end_date: Timestamp
  token: String
}

input RegisterDto {
  email: String
  password: String!
  phone: String!
  firstName: String
  lastName: String
}

input LoginDto {
  email: String!
  password: String!
}

input CreateRoleDto {
  name: String!
  description: String
}

input UpdateRoleDto {
  name: String
  description: String
}

input CreatePermissionDto {
  name: String!
  description: String
  resource: String
  action: String
}

input CreatePermissionHierarchyDto {
  parentPermissionId: String!
  childPermissionId: String!
  description: String
}

input InsertPoliticalPartyInput {
  name: String!
  short_name: String!
}

input UpdatePoliticalPartyInput {
  name: String
  short_name: String
}

input CreateCandidateInput {
  full_name: String!
  political_party_id: String
}

input UpdateCandidateInput {
  full_name: String
  political_party_id: String
  id: String!
}