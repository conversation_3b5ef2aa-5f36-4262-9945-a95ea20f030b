# ===============================
# MINOS BACKEND - CONFIGURATION
# ===============================

# 🌐 APPLICATION SETTINGS
PORT=3001
NODE_ENV=test

# 📊 DATABASE CONFIGURATION (PostgreSQL)
DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=minos_test

# 🔐 JWT CONFIGURATION
# ⚠️ IMPORTANT: Change these secrets in production!
JWT_SECRET=minos_super_secret_jwt_key_change_in_production_2024
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=minos_super_secret_refresh_key_change_in_production_2024
JWT_REFRESH_EXPIRES_IN=30d

# 🌐 BACKEND URL (for redirects and webhooks)
BACKEND_URL=http://localhost:3001

# 🔒 ENCRYPTION KEY (base64 encoded)
# Generate with: openssl rand -base64 32
ENCRYPTION_KEY_BASE64=ah722Bf7S1MbHSArIpAqJpumpVjshBJ3QzeXHVb/12U=

# 📱 WATI WHATSAPP INTEGRATION (optional)
WATI_BASE_URL=https://live-mt-server.wati.io/
WATI_TOKEN=your_wati_api_token_here

# 📧 EMAIL CONFIGURATION (optional)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_FROM=<EMAIL>

# ===============================
# SETUP INSTRUCTIONS
# ===============================
#
# 1. Copy this file to .env:
#    cp env.example .env
#
# 2. Update the database credentials above
#
# 3. Change JWT secrets for production
#
# 4. Run initial setup:
#    npm run setup:initial
#
# 5. Start the application:
#    npm run start:dev
#
# 🎉 Your app will be running at http://localhost:3000
# 📊 GraphQL Playground: http://localhost:3000/graphql
# 👤 Default admin: <EMAIL> / Admin123!
#
# ===============================